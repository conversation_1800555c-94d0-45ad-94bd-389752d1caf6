<script lang="ts">
  import FeatureCard from '../ui/FeatureCard.svelte';
  import AnimatedContainer from '$lib/components/ui/AnimatedContainer.svelte';
  import {
    Target,
    Clock,
    ChartBar,
    Shield,
    Globe,
    CheckCircle,
    FileText,
    PenTool,
    Award,
    ArrowRight,
    MessageSquare,
    Sparkles,
  } from 'lucide-svelte';

  // Features object with improved wording
  const features = {
    // Automated Apply features
    automatedApply: {
      title: 'Effortless Job Applications',
      description:
        'Submit applications to hundreds of positions with our streamlined one-click system.',
      secondary: [
        {
          icon: Clock,
          title: 'Reclaim Your Time',
          description:
            'Save hours daily by automating repetitive tasks and focus on interview preparation.',
        },
        {
          icon: ChartBar,
          title: 'Performance Insights',
          description:
            'Gain valuable analytics to optimize your application strategy and improve results.',
        },
        {
          icon: Shield,
          title: 'Resume Enhancement',
          description:
            'Receive tailored suggestions to strengthen your resume for specific opportunities.',
        },
        {
          icon: Globe,
          title: 'Universal Platform Support',
          description:
            'Seamlessly works with LinkedIn, Indeed, Glassdoor, ZipRecruiter, and many more.',
        },
      ],
    },

    // Job Tracker features
    jobTracker: {
      title: 'Comprehensive Application Tracking',
      description: 'Monitor all your job applications in one intuitive, centralized dashboard.',
      secondary: [
        {
          icon: CheckCircle,
          title: 'Real-time Status Updates',
          description: 'Track your progress through each stage of the hiring process with clarity.',
        },
        {
          icon: Clock,
          title: 'Interview Management',
          description:
            'Organize and prepare for upcoming interviews with smart scheduling and reminders.',
        },
        {
          icon: ChartBar,
          title: 'Strategic Analytics',
          description:
            'Visualize your job search journey with detailed metrics and actionable insights.',
        },
        {
          icon: Shield,
          title: 'Enterprise-grade Security',
          description:
            'Rest assured your career data is protected with advanced encryption and privacy controls.',
        },
      ],
    },

    // Resume Builder features
    resumeBuilder: {
      title: 'Professional Resume Creator',
      description: 'Craft standout resumes that capture attention with our intuitive builder.',
      main: [
        {
          icon: FileText,
          title: 'Expert-designed Templates',
          description:
            'Choose from dozens of ATS-optimized templates crafted by hiring professionals.',
        },
        {
          icon: PenTool,
          title: 'Intuitive Customization',
          description: 'Personalize every aspect of your resume with our user-friendly editor.',
        },
      ],
      secondary: [
        {
          icon: Target,
          title: 'ATS-Friendly Formatting',
          description:
            'Ensure your resume successfully navigates through automated screening systems.',
        },
        {
          icon: Award,
          title: 'Strategic Skills Showcase',
          description:
            'Automatically highlight relevant qualifications based on target job descriptions.',
        },
        {
          icon: Globe,
          title: 'Versatile Export Options',
          description:
            'Download your polished resume in PDF, DOCX, or plain text formats as needed.',
        },
        {
          icon: Shield,
          title: 'Multiple Resume Versions',
          description:
            'Create and manage specialized resumes tailored for different career opportunities.',
        },
      ],
    },

    // Co-Pilot features
    coPilot: {
      title: 'AI Career Co-Pilot',
      description: 'Navigate your career journey with AI-powered guidance every step of the way.',
      secondary: [
        {
          icon: MessageSquare,
          title: 'AI Interview Coach',
          description:
            'Practice with realistic mock interviews tailored to your industry with instant feedback.',
        },
        {
          icon: Sparkles,
          title: 'Personalized Insights',
          description: 'Receive custom career advice based on your skills, experience, and goals.',
        },
        {
          icon: Target,
          title: 'Job Match Analysis',
          description:
            'Get AI-powered compatibility scores for job listings based on your profile.',
        },
        {
          icon: Shield,
          title: 'Career Strategy Planning',
          description:
            'Develop a strategic roadmap to achieve your long-term professional objectives.',
        },
      ],
    },
  };
</script>

<section
  id="services"
  class="from-background via-muted/5 to-background relative overflow-hidden bg-gradient-to-br py-20">
  <!-- Background decoration -->
  <div class="absolute inset-0 opacity-5">
    <div class="animate-float absolute right-20 top-20 delay-1000">
      <div class="bg-primary/20 h-32 w-32 rounded-full"></div>
    </div>
    <div class="animate-float absolute bottom-20 left-20 delay-500">
      <div class="bg-accent/20 h-24 w-24 rounded-full"></div>
    </div>
  </div>

  <div class="container relative mx-auto px-4">
    <!-- Automated Apply Section -->
    <AnimatedContainer animation="fade-in-up" triggerOnScroll={true}>
      <div class="mb-20 flex flex-col">
        <!-- 2 columns row -->
        <div class="floating-card mb-8 grid grid-cols-1 overflow-hidden lg:grid-cols-2">
          <div class="gradient-card flex flex-col justify-center p-12 lg:p-16">
            <div class="max-w-lg">
              <h3 class="mb-6 text-4xl font-light lg:text-5xl">{features.automatedApply.title}</h3>
              <p class="text-muted-foreground mb-8 text-lg">
                {features.automatedApply.description}
              </p>
              <a
                href="/auto-apply"
                class="gradient-primary hover-lift shadow-colored group inline-flex w-fit items-center rounded-lg px-8 py-4 text-lg font-medium text-white transition-all">
                Learn More <ArrowRight
                  class="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </a>
            </div>
          </div>
          <div
            class="bg-grid from-primary/10 to-accent/10 relative min-h-[300px] bg-gradient-to-br lg:min-h-[400px]">
            <!-- Decorative elements -->
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="animate-float bg-primary/20 h-20 w-20 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </AnimatedContainer>

    <!-- Feature Cards -->
    <div class="mb-20 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
      {#each features.automatedApply.secondary as feature, index}
        <AnimatedContainer animation="fade-in-up" delay={200 + index * 100} triggerOnScroll={true}>
          <div class="floating-card hover-lift group p-8 transition-all duration-300">
            <div
              class="from-primary/20 to-accent/20 mb-6 flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br transition-all duration-300 group-hover:scale-110">
              <svelte:component
                this={feature.icon}
                class="text-primary h-7 w-7 transition-all duration-300 group-hover:scale-110" />
            </div>
            <h3 class="mb-4 text-xl font-semibold">{feature.title}</h3>
            <p class="text-muted-foreground">{feature.description}</p>
          </div>
        </AnimatedContainer>
      {/each}
    </div>

    <div
      class="border-border md:grid-cols-16 flex flex-col border [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]">
      <div
        class="bg-grid bg-grid-primary/20 dark:bg-grid-primary/40 border-border col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l">
      </div>
      <div
        class="p-15 text-foreground col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center">
        <div
          class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]">
          <div class="flex flex-col gap-20">
            <h3 class="font-light! max-w-3xs text-6xl">{features.jobTracker.title}</h3>
            <p class="typography font-montreal text-xl">
              {features.jobTracker.description}
            </p>
            <a
              href="/job-tracker"
              class="bg-primary text-primary-foreground hover:bg-primary/90 dark:hover:bg-primary/70 group flex w-48 flex-row items-center justify-between rounded-md px-6 py-3 transition-all duration-200">
              Learn More <ArrowRight
                class="ml-2 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1" />
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 4 columns row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
      {#each features.jobTracker.secondary as feature}
        <FeatureCard icon={feature.icon} title={feature.title} description={feature.description} />
      {/each}
    </div>

    <div
      class="md:grid-cols-16 flex flex-col border border-t-neutral-500 [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]">
      <div
        class="p-15 text-primary col-span-8 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center">
        <div
          class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]">
          <div class="flex flex-col gap-20">
            <h3 class="font-light! max-w-3xs text-6xl">{features.resumeBuilder.title}</h3>
            <p class="typography font-montreal text-xl">
              {features.resumeBuilder.description}
            </p>
            <a
              href="/resume-builder"
              class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">
              Learn More <ArrowRight class="ml-2 h-4 w-4" />
            </a>
          </div>
        </div>
      </div>
      <div
        class="border-border bg-grid col-span-8 col-start-9 row-span-8 row-start-1 border border-b border-r border-t">
      </div>
    </div>

    <!-- 4 columns row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
      {#each features.resumeBuilder.secondary as feature}
        <FeatureCard icon={feature.icon} title={feature.title} description={feature.description} />
      {/each}
    </div>

    <!-- Co-Pilot Section -->
    <div
      class="border-border md:grid-cols-16 flex flex-col border border-t [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]">
      <div
        class="border-border bg-grid bg-grid-primary/20 dark:bg-grid-primary/40 col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l">
      </div>
      <div
        class="p-15 text-text-primary col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center">
        <div
          class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]">
          <div class="flex flex-col gap-20">
            <h3 class="font-light! max-w-3xs text-6xl">{features.coPilot.title}</h3>
            <p class="typography font-montreal text-xl">
              {features.coPilot.description}
            </p>
            <a
              href="/co-pilot"
              class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">
              Learn More <ArrowRight class="ml-2 h-4 w-4" />
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 4 columns row for Co-Pilot features -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
      {#each features.coPilot.secondary as feature}
        <FeatureCard icon={feature.icon} title={feature.title} description={feature.description} />
      {/each}
    </div>
  </div>
</section>
