<script lang="ts">
  import { cn } from '$lib/utils';

  interface Props {
    variant?: 'default' | 'floating' | 'glass' | 'gradient';
    hover?: 'lift' | 'scale' | 'glow' | 'none';
    className?: string;
    children?: any;
    onclick?: () => void;
    href?: string;
  }

  let {
    variant = 'default',
    hover = 'lift',
    className = '',
    children,
    onclick,
    href
  }: Props = $props();

  const baseClasses = 'transition-all duration-300 ease-out';
  
  const variantClasses = {
    default: 'modern-card',
    floating: 'floating-card',
    glass: 'glass-card',
    gradient: 'gradient-card border border-border/50 rounded-lg shadow-soft'
  };

  const hoverClasses = {
    lift: 'hover-lift',
    scale: 'hover-scale',
    glow: 'hover-glow',
    none: ''
  };

  const cardClasses = $derived(() => 
    cn(
      baseClasses,
      variantClasses[variant],
      hoverClasses[hover],
      onclick || href ? 'cursor-pointer interactive' : '',
      className
    )
  );

  function handleClick() {
    if (onclick) {
      onclick();
    }
  }
</script>

{#if href}
  <a {href} class={cardClasses} onclick={handleClick}>
    {@render children?.()}
  </a>
{:else}
  <div class={cardClasses} onclick={handleClick} role={onclick ? 'button' : undefined} tabindex={onclick ? 0 : undefined}>
    {@render children?.()}
  </div>
{/if}
