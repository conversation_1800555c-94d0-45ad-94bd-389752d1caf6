<script lang="ts">
  import Logo from '$components/ui/Logo.svelte';
  import { Button } from '$lib/components/ui/button';
  import { ArrowRightIcon, Menu, X } from 'lucide-svelte';
  import type { HTMLAttributes } from 'svelte/elements';
  import * as NavigationMenu from '$lib/components/ui/navigation-menu';
  import { navigationMenuTriggerStyle } from '$lib/components/ui/navigation-menu/navigation-menu-trigger.svelte';
  import { cn } from '$lib/utils';
  import { onMount } from 'svelte';
  import { graphqlRequest, COLLECTIONS_QUERY } from '$lib/graphql/client';
  import type { Collection } from '$lib/graphql/types';

  const { currentUser = null, data = {} } = $props();

  let jobCollections = $state<Collection[]>([]);
  let topCollections = $state<Collection[]>([]);

  // Products dropdown links
  const productLinks = [
    {
      title: 'Auto Apply',
      href: '/auto-apply',
      description: 'Automatically apply to jobs that match your criteria',
    },
    {
      title: 'Job Tracker',
      href: '/job-tracker',
      description: 'Track your job applications and interviews',
    },
    {
      title: 'Resume Builder',
      href: '/resume-builder',
      description: 'Create professional resumes with AI assistance',
    },
    {
      title: 'AI Co-Pilot',
      href: '/co-pilot',
      description: 'Get personalized job search guidance',
    },
    {
      title: 'For Recruiters',
      href: '/recruiters',
      description: 'Tools and solutions for recruiting teams',
    },
    {
      title: 'For Employers',
      href: '/employers',
      description: 'Hire top talent with our platform',
    },
    {
      title: 'Browser Extension',
      href: '/extension',
      description: 'Apply to jobs directly from job boards',
    },
  ];

  // Resources dropdown links
  const resourceLinks = [
    { title: 'Free Tools', href: '/resources', description: 'Collection of free job search tools' },
    {
      title: 'Resume Templates',
      href: '/resources/resume-templates',
      description: 'Professional resume templates',
    },
    {
      title: 'Cover Letter Templates',
      href: '/resources/cover-letters',
      description: 'Winning cover letter examples',
    },
    {
      title: 'ATS Resume Checker',
      href: '/resources/ats-optimization/checker',
      description: 'Optimize your resume for ATS systems',
    },
    {
      title: 'Interview Questions',
      href: '/resources/interview-prep/question-database',
      description: 'Practice common interview questions',
    },
    {
      title: 'Salary Tools',
      href: '/resources/salary-tools',
      description: 'Research and negotiate salaries',
    },
  ];

  // Load job collections
  async function loadJobCollections() {
    try {
      // Try to use collections from data prop first
      if (data?.jobCollections && Array.isArray(data.jobCollections)) {
        jobCollections = data.jobCollections;
      } else {
        // Fallback to GraphQL request
        const result = await graphqlRequest<{ collections: Collection[] }>(COLLECTIONS_QUERY);
        if (result.errors) {
          throw new Error(result.errors[0].message);
        }
        jobCollections = result.data?.collections || [];
      }

      // Get top 10 collections (alphabetically sorted)
      topCollections = jobCollections.slice(0, 10);
    } catch (error) {
      console.error('Error loading job collections for header:', error);
    }
  }

  // Initialize on mount
  onMount(() => {
    loadJobCollections();
  });

  type ListItemProps = HTMLAttributes<HTMLAnchorElement> & {
    title: string;
    href: string;
    description: string;
  };
</script>

<!-- Header matching the picture design -->
<header>
  <div class="mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <!-- Logo -->
      <div class="flex items-center">
        <Logo class="h-8 w-8" />
        <span class="ml-2 text-xl font-semibold text-gray-900">Hirli</span>
      </div>

      <!-- Centered Navigation Pills -->
      {#snippet ListItem({
        title,
        href,
        description,
        class: className,
        ...restProps
      }: ListItemProps)}
        <li>
          <NavigationMenu.Link>
            {#snippet child()}
              <a
                {href}
                class={cn(
                  'hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors',
                  className
                )}
                {...restProps}>
                <div class="text-sm font-medium leading-none">{title}</div>
                <p class="text-muted-foreground line-clamp-2 text-sm leading-snug">{description}</p>
              </a>
            {/snippet}
          </NavigationMenu.Link>
        </li>
      {/snippet}

      <NavigationMenu.Root>
        <NavigationMenu.List>
          <!-- Products Megamenu -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Products</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                <li class="row-span-3">
                  <NavigationMenu.Link>
                    {#snippet child()}
                      <a
                        class="from-muted/50 to-muted flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b p-6 no-underline outline-none focus:shadow-md"
                        href="/auto-apply">
                        <Logo class="size-6" />
                        <div class="mb-2 mt-4 text-lg font-medium">Auto Apply</div>
                        <p class="text-muted-foreground text-sm leading-tight">
                          Automatically apply to jobs that match your criteria and preferences.
                        </p>
                      </a>
                    {/snippet}
                  </NavigationMenu.Link>
                </li>

                {#each productLinks.slice(1) as product}
                  {@render ListItem({
                    href: product.href,
                    title: product.title,
                    description: product.description,
                  })}
                {/each}
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Resources Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Resources</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid w-[200px] gap-1 p-2">
                {#each resourceLinks as resource}
                  <li>
                    <a
                      href={resource.href}
                      class="hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 text-sm leading-none no-underline outline-none transition-colors">
                      {resource.title}
                    </a>
                  </li>
                {/each}
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Categories Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Categories</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid w-[200px] gap-1 p-2">
                {#each topCollections as collection}
                  <li>
                    <a
                      href="/jobs?collection={collection.slug}"
                      class="hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 text-sm leading-none no-underline outline-none transition-colors">
                      {collection.name}
                    </a>
                  </li>
                {/each}
                <li>
                  <a
                    href="/jobs"
                    class="hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 text-sm leading-none no-underline outline-none transition-colors">
                    Browse All Jobs
                  </a>
                </li>
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Direct Links -->
          <NavigationMenu.Item>
            <NavigationMenu.Link href="/pricing" class={cn(navigationMenuTriggerStyle())}>
              Pricing
            </NavigationMenu.Link>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link href="/help" class={cn(navigationMenuTriggerStyle())}>
              Help
            </NavigationMenu.Link>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link href="/contact" class={cn(navigationMenuTriggerStyle())}>
              Contact Sales
            </NavigationMenu.Link>
          </NavigationMenu.Item>
        </NavigationMenu.List>
      </NavigationMenu.Root>

      <!-- Right Actions -->
      <div class="flex items-center space-x-4">
        <div class="border-border/50 flex flex-col gap-2">
          <Button
            variant="outline"
            size="lg"
            href="/dashboard"
            class="gradient-primary justify-start text-white">
            Launch <ArrowRightIcon class="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</header>
