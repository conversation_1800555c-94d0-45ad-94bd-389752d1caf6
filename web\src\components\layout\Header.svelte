<script lang="ts">
  import Logo from '$components/ui/Logo.svelte';
  import { Button } from '$lib/components/ui/button';
  import {
    ArrowRightIcon,
    Zap,
    Target,
    FileText,
    Bot,
    Users,
    Building,
    Chrome,
    Newspaper,
    TrendingUp,
  } from 'lucide-svelte';
  import type { HTMLAttributes } from 'svelte/elements';
  import * as NavigationMenu from '$lib/components/ui/navigation-menu';
  import { navigationMenuTriggerStyle } from '$lib/components/ui/navigation-menu/navigation-menu-trigger.svelte';
  import { cn } from '$lib/utils';
  import { onMount } from 'svelte';
  import { graphqlRequest, COLLECTIONS_QUERY } from '$lib/graphql/client';
  import type { Collection } from '$lib/graphql/types';

  const { currentUser = null, data = {} } = $props();

  let jobCollections = $state<Collection[]>([]);
  let topCollections = $state<Collection[]>([]);

  // Products dropdown links with icons
  const productLinks = [
    {
      title: 'Auto Apply',
      href: '/auto-apply',
      description: 'Automatically apply to jobs that match your criteria',
      icon: Zap,
    },
    {
      title: 'Job Tracker',
      href: '/job-tracker',
      description: 'Track your job applications and interviews',
      icon: Target,
    },
    {
      title: 'Resume Builder',
      href: '/resume-builder',
      description: 'Create professional resumes with AI assistance',
      icon: FileText,
    },
    {
      title: 'AI Co-Pilot',
      href: '/co-pilot',
      description: 'Get personalized job search guidance',
      icon: Bot,
    },
    {
      title: 'Browser Extension',
      href: '/extension',
      description: 'Apply to jobs directly from job boards',
      icon: Chrome,
    },
  ];

  // News sections for the right side
  const newsItems = [
    {
      title: 'Resume is now compatible with Figma Sites',
      date: 'May 21, 2025',
      description: 'Build better resumes with our new Figma integration',
      image: '/news/figma-integration.jpg',
    },
    {
      title: 'Premium Monotype Fonts and Branded Sharing',
      date: 'April 9, 2025',
      description: 'Enhanced typography and sharing options',
      image: '/news/premium-fonts.jpg',
    },
  ];

  // Resources dropdown links
  const resourceLinks = [
    { title: 'Free Tools', href: '/resources', description: 'Collection of free job search tools' },
    {
      title: 'Resume Templates',
      href: '/resources/resume-templates',
      description: 'Professional resume templates',
    },
    {
      title: 'Cover Letter Templates',
      href: '/resources/cover-letters',
      description: 'Winning cover letter examples',
    },
    {
      title: 'ATS Resume Checker',
      href: '/resources/ats-optimization/checker',
      description: 'Optimize your resume for ATS systems',
    },
    {
      title: 'Interview Questions',
      href: '/resources/interview-prep/question-database',
      description: 'Practice common interview questions',
    },
    {
      title: 'Salary Tools',
      href: '/resources/salary-tools',
      description: 'Research and negotiate salaries',
    },
  ];

  // Load job collections
  async function loadJobCollections() {
    try {
      // Try to use collections from data prop first
      if (data?.jobCollections && Array.isArray(data.jobCollections)) {
        jobCollections = data.jobCollections;
      } else {
        // Fallback to GraphQL request
        const result = await graphqlRequest<{ collections: Collection[] }>(COLLECTIONS_QUERY);
        if (result.errors) {
          throw new Error(result.errors[0].message);
        }
        jobCollections = result.data?.collections || [];
      }

      // Get top 10 collections (alphabetically sorted)
      topCollections = jobCollections.slice(0, 10);
    } catch (error) {
      console.error('Error loading job collections for header:', error);
    }
  }

  // Initialize on mount
  onMount(() => {
    loadJobCollections();
  });

  type ListItemProps = HTMLAttributes<HTMLAnchorElement> & {
    title: string;
    href: string;
    description: string;
  };
</script>

<!-- Header matching the picture design -->
<header>
  <div class="mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <!-- Logo -->
      <div class="flex items-center">
        <Logo class="h-8 w-8" />
        <span class="ml-2 text-xl font-semibold text-gray-900">Hirli</span>
      </div>

      <!-- Centered Navigation Pills -->
      {#snippet ListItem({
        title,
        href,
        description,
        class: className,
        ...restProps
      }: ListItemProps)}
        <li>
          <NavigationMenu.Link>
            {#snippet child()}
              <a
                {href}
                class={cn(
                  'hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors',
                  className
                )}
                {...restProps}>
                <div class="text-sm font-medium leading-none">{title}</div>
                <p class="text-muted-foreground line-clamp-2 text-sm leading-snug">{description}</p>
              </a>
            {/snippet}
          </NavigationMenu.Link>
        </li>
      {/snippet}

      <NavigationMenu.Root>
        <NavigationMenu.List>
          <!-- Products Megamenu -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Products</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <div class="grid w-[700px] grid-cols-2 gap-4 p-3">
                <!-- Left Column - Products with Icons -->
                <div>
                  <ul class="space-y-2">
                    {#each productLinks as product}
                      {@const Icon = product.icon}
                      <li>
                        <NavigationMenu.Link
                          href={product.href}
                          class="flex flex-row items-center gap-4">
                          <div
                            class="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100">
                            <Icon class="h-5 w-5 text-gray-600" />
                          </div>
                          <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900">{product.title}</div>
                            <p class="text-xs text-gray-500">{product.description}</p>
                          </div>
                        </NavigationMenu.Link>
                      </li>
                    {/each}
                  </ul>
                </div>

                <!-- Right Column - What's New -->
                <div class="bg-secondary rounded-md p-4">
                  <div class="mb-4 flex items-center justify-between">
                    <h3 class="text-sm font-medium text-gray-900">What's New</h3>
                    <a href="/news" class="text-xs text-blue-600 hover:text-blue-700">View all</a>
                  </div>
                  <ul class="space-y-4">
                    {#each newsItems as news}
                      <li>
                        <NavigationMenu.Link
                          href="/news/{news.title.toLowerCase().replace(/\s+/g, '-')}"
                          class="block rounded-lg p-3 transition-colors hover:bg-gray-50">
                          <div class="mb-1 text-xs text-gray-500">{news.date}</div>
                          <div class="mb-2 text-sm font-medium text-gray-900">{news.title}</div>
                          <div
                            class="mb-2 h-20 w-full rounded-md bg-gradient-to-br from-blue-100 to-purple-100">
                          </div>
                        </NavigationMenu.Link>
                      </li>
                    {/each}
                  </ul>
                </div>
              </div>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Categories Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Sectors</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid w-[200px] gap-1 p-2">
                {#each topCollections as collection}
                  <li>
                    <a
                      href="/jobs?collection={collection.slug}"
                      class="hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 text-sm leading-none no-underline outline-none transition-colors">
                      {collection.name}
                    </a>
                  </li>
                {/each}
                <li>
                  <a
                    href="/jobs"
                    class="hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 text-sm leading-none no-underline outline-none transition-colors">
                    Browse All Jobs
                  </a>
                </li>
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Resources Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Resources</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid w-[200px] gap-1 p-2">
                {#each resourceLinks as resource}
                  <li>
                    <NavigationMenu.Link
                      href={resource.href}
                      class="hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 text-sm leading-none no-underline outline-none transition-colors">
                      {resource.title}
                    </NavigationMenu.Link>
                  </li>
                {/each}
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link href="/pricing" class={cn(navigationMenuTriggerStyle())}>
              Pricing
            </NavigationMenu.Link>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link href="/recruiters" class={cn(navigationMenuTriggerStyle())}>
              Recruiters
            </NavigationMenu.Link>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link href="/employers" class={cn(navigationMenuTriggerStyle())}>
              Employers
            </NavigationMenu.Link>
          </NavigationMenu.Item>
        </NavigationMenu.List>
      </NavigationMenu.Root>

      <!-- Right Actions -->
      <div class="flex items-center space-x-4">
        <div class="border-border/50 flex flex-col gap-2">
          <Button
            variant="outline"
            size="lg"
            href="/dashboard"
            class="gradient-primary justify-start text-white">
            Launch <ArrowRightIcon class="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</header>
