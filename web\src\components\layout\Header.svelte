<script lang="ts">
  import Logo from '$components/ui/Logo.svelte';
  import { Button } from '$lib/components/ui/button';
  import { ArrowRightIcon, Menu, X } from 'lucide-svelte';
  import type { HTMLAttributes } from 'svelte/elements';
  import * as NavigationMenu from '$lib/components/ui/navigation-menu';
  import { navigationMenuTriggerStyle } from '$lib/components/ui/navigation-menu/navigation-menu-trigger.svelte';
  import { cn } from '$lib/utils';
  const { currentUser = null } = $props();

  const components: { title: string; href: string }[] = [
    {
      title: 'Alert Dialog',
      href: '/docs/primitives/alert-dialog',
    },
    {
      title: 'Hover Card',
      href: '/docs/primitives/hover-card',
    },
    {
      title: 'Progress',
      href: '/docs/primitives/progress',
    },
    {
      title: 'Scroll-area',
      href: '/docs/primitives/scroll-area',
    },
    {
      title: 'Tabs',
      href: '/docs/primitives/tabs',
    },
    {
      title: 'Tooltip',
      href: '/docs/primitives/tooltip',
    },
  ];

  type ListItemProps = HTMLAttributes<HTMLAnchorElement> & {
    title: string;
    href: string;
    content: string;
  };
</script>

<!-- Header matching the picture design -->
<header>
  <div class="mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <!-- Logo -->
      <div class="flex items-center">
        <Logo class="h-8 w-8" />
        <span class="ml-2 text-xl font-semibold text-gray-900">Hirli</span>
      </div>

      <!-- Centered Navigation Pills -->
      {#snippet ListItem({ title, href, class: className, ...restProps }: ListItemProps)}
        <li>
          <NavigationMenu.Link>
            {#snippet child()}
              <a
                {href}
                class={cn(
                  'hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors',
                  className
                )}
                {...restProps}>
                <div class="text-sm font-medium leading-none">{title}</div>
              </a>
            {/snippet}
          </NavigationMenu.Link>
        </li>
      {/snippet}

      <NavigationMenu.Root>
        <NavigationMenu.List>
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Getting started</NavigationMenu.Trigger>

            <NavigationMenu.Content>
              <ul class="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                <li class="row-span-3">
                  <NavigationMenu.Link>
                    {#snippet child()}
                      <a
                        class="from-muted/50 to-muted flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b p-6 no-underline outline-none focus:shadow-md"
                        href="/">
                        <Logo class="size-6" />
                        <div class="mb-2 mt-4 text-lg font-medium">shadcn-svelte</div>
                        <p class="text-muted-foreground text-sm leading-tight">
                          Beautifully designed components built with Bits UI and Tailwind CSS.
                        </p>
                      </a>
                    {/snippet}
                  </NavigationMenu.Link>
                </li>

                {@render ListItem({
                  href: '/docs',
                  title: 'Introduction',
                  content: 'Re-usable components built using Bits UI and Tailwind CSS.',
                })}

                {@render ListItem({
                  href: '/docs/installation',
                  title: 'Installation',
                  content: 'How to install dependencies and structure your app.',
                })}

                {@render ListItem({
                  href: '/docs/primitives/typography',
                  title: 'Typography',
                  content: 'Styles for headings, paragraphs, lists...etc',
                })}
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Components</NavigationMenu.Trigger>

            <NavigationMenu.Content>
              <ul class="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                {#each components as component, i (i)}
                  {@render ListItem({
                    href: component.href,
                    title: component.title,
                  })}
                {/each}
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link href="/docs" class={cn(navigationMenuTriggerStyle())}>
              Documentation
            </NavigationMenu.Link>
          </NavigationMenu.Item>
        </NavigationMenu.List>
      </NavigationMenu.Root>

      <!-- Right Actions -->
      <div class="flex items-center space-x-4">
        <div class="border-border/50 flex flex-col gap-2">
          <Button
            variant="outline"
            size="lg"
            href="/auth/register"
            class="gradient-primary justify-start text-white">
            Launch <ArrowRightIcon class="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</header>
