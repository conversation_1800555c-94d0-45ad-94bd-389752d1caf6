<script lang="ts">
  import Logo from '$components/ui/Logo.svelte';
  import { Button } from '$lib/components/ui/button';
  import { ArrowRightIcon, Menu, X } from 'lucide-svelte';
  import type { HTMLAttributes } from 'svelte/elements';
  import * as NavigationMenu from '$lib/components/ui/navigation-menu';
  import { navigationMenuTriggerStyle } from '$lib/components/ui/navigation-menu/navigation-menu-trigger.svelte';
  import { cn } from '$lib/utils';
  const { currentUser = null } = $props();

  // Products dropdown links
  const productLinks = [
    {
      title: 'Auto Apply',
      href: '/auto-apply',
      description: 'Automatically apply to jobs that match your criteria',
    },
    {
      title: 'Job Tracker',
      href: '/job-tracker',
      description: 'Track your job applications and interviews',
    },
    {
      title: 'Resume Builder',
      href: '/resume-builder',
      description: 'Create professional resumes with AI assistance',
    },
    {
      title: 'AI Co-Pilot',
      href: '/co-pilot',
      description: 'Get personalized job search guidance',
    },
    {
      title: 'For Recruiters',
      href: '/recruiters',
      description: 'Tools and solutions for recruiting teams',
    },
    {
      title: 'For Employers',
      href: '/employers',
      description: 'Hire top talent with our platform',
    },
    {
      title: 'Browser Extension',
      href: '/extension',
      description: 'Apply to jobs directly from job boards',
    },
  ];

  // Community dropdown links
  const communityLinks = [
    {
      title: 'TikTok',
      href: 'https://tiktok.com/hirli',
      description: 'Follow us for job search tips',
    },
    {
      title: 'LinkedIn',
      href: 'https://linkedin.com/company/hirli',
      description: 'Connect with our professional community',
    },
    {
      title: 'X (Twitter)',
      href: 'https://x.com/hirliapp',
      description: 'Get the latest updates and news',
    },
    {
      title: 'Instagram',
      href: 'https://instagram.com/hirliapp',
      description: 'Behind the scenes content',
    },
    {
      title: 'Submit Feedback',
      href: 'https://autoapply.featurebase.app/',
      description: 'Help us improve the platform',
    },
    {
      title: 'Roadmap',
      href: 'https://autoapply.featurebase.app/roadmap',
      description: "See what we're building next",
    },
  ];

  // Resources dropdown links
  const resourceLinks = [
    { title: 'Free Tools', href: '/resources', description: 'Collection of free job search tools' },
    {
      title: 'Resume Templates',
      href: '/resources/resume-templates',
      description: 'Professional resume templates',
    },
    {
      title: 'Cover Letter Templates',
      href: '/resources/cover-letters',
      description: 'Winning cover letter examples',
    },
    {
      title: 'ATS Resume Checker',
      href: '/resources/ats-optimization/checker',
      description: 'Optimize your resume for ATS systems',
    },
    {
      title: 'Interview Questions',
      href: '/resources/interview-prep/question-database',
      description: 'Practice common interview questions',
    },
    {
      title: 'Salary Tools',
      href: '/resources/salary-tools',
      description: 'Research and negotiate salaries',
    },
  ];

  // Categories dropdown links
  const categoryLinks = [
    { title: 'Tech Jobs', href: '/tech-jobs', description: 'Software engineering and tech roles' },
    { title: 'Remote Jobs', href: '/remote-jobs', description: 'Work from anywhere opportunities' },
    { title: 'Entry Level', href: '/entry-level', description: 'Perfect for new graduates' },
    { title: 'Browse All Jobs', href: '/jobs', description: 'Explore all available positions' },
  ];

  type ListItemProps = HTMLAttributes<HTMLAnchorElement> & {
    title: string;
    href: string;
    description: string;
  };
</script>

<!-- Header matching the picture design -->
<header>
  <div class="mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <!-- Logo -->
      <div class="flex items-center">
        <Logo class="h-8 w-8" />
        <span class="ml-2 text-xl font-semibold text-gray-900">Hirli</span>
      </div>

      <!-- Centered Navigation Pills -->
      {#snippet ListItem({
        title,
        href,
        description,
        class: className,
        ...restProps
      }: ListItemProps)}
        <li>
          <NavigationMenu.Link>
            {#snippet child()}
              <a
                {href}
                class={cn(
                  'hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors',
                  className
                )}
                {...restProps}>
                <div class="text-sm font-medium leading-none">{title}</div>
                <p class="text-muted-foreground line-clamp-2 text-sm leading-snug">{description}</p>
              </a>
            {/snippet}
          </NavigationMenu.Link>
        </li>
      {/snippet}

      <NavigationMenu.Root>
        <NavigationMenu.List>
          <!-- Products Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Products</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                {#each productLinks as product}
                  {@render ListItem({
                    href: product.href,
                    title: product.title,
                    description: product.description,
                  })}
                {/each}
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Community Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Community</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                {#each communityLinks as community}
                  {@render ListItem({
                    href: community.href,
                    title: community.title,
                    description: community.description,
                  })}
                {/each}
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Resources Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Resources</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                {#each resourceLinks as resource}
                  {@render ListItem({
                    href: resource.href,
                    title: resource.title,
                    description: resource.description,
                  })}
                {/each}
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Categories Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger>Categories</NavigationMenu.Trigger>
            <NavigationMenu.Content>
              <ul class="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-1 lg:w-[500px]">
                {#each categoryLinks as category}
                  {@render ListItem({
                    href: category.href,
                    title: category.title,
                    description: category.description,
                  })}
                {/each}
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Direct Links -->
          <NavigationMenu.Item>
            <NavigationMenu.Link href="/pricing" class={cn(navigationMenuTriggerStyle())}>
              Pricing
            </NavigationMenu.Link>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link href="/help" class={cn(navigationMenuTriggerStyle())}>
              Help
            </NavigationMenu.Link>
          </NavigationMenu.Item>

          <NavigationMenu.Item>
            <NavigationMenu.Link href="/contact" class={cn(navigationMenuTriggerStyle())}>
              Contact Sales
            </NavigationMenu.Link>
          </NavigationMenu.Item>
        </NavigationMenu.List>
      </NavigationMenu.Root>

      <!-- Right Actions -->
      <div class="flex items-center space-x-4">
        <div class="border-border/50 flex flex-col gap-2">
          <Button
            variant="outline"
            size="lg"
            href="/auth/register"
            class="gradient-primary justify-start text-white">
            Launch <ArrowRightIcon class="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</header>
