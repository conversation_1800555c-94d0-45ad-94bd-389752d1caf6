<script lang="ts">
  import { onMount } from 'svelte';
  import { cn } from '$lib/utils';

  interface Props {
    animation?: 'fade-in-up' | 'fade-in-down' | 'fade-in-left' | 'fade-in-right' | 'scale-in' | 'float';
    delay?: number;
    duration?: number;
    className?: string;
    children?: any;
    triggerOnScroll?: boolean;
    threshold?: number;
  }

  let {
    animation = 'fade-in-up',
    delay = 0,
    duration = 600,
    className = '',
    children,
    triggerOnScroll = false,
    threshold = 0.1
  }: Props = $props();

  let elementRef: HTMLElement;
  let isVisible = $state(!triggerOnScroll);
  let hasAnimated = $state(false);

  onMount(() => {
    if (triggerOnScroll && elementRef) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && !hasAnimated) {
              isVisible = true;
              hasAnimated = true;
            }
          });
        },
        { threshold }
      );

      observer.observe(elementRef);

      return () => {
        observer.disconnect();
      };
    } else {
      // Trigger animation immediately if not scroll-triggered
      setTimeout(() => {
        isVisible = true;
      }, delay);
    }
  });

  const animationClass = $derived(() => {
    if (!isVisible) return 'opacity-0';
    return `animate-${animation}`;
  });

  const delayClass = $derived(() => {
    if (delay > 0) {
      if (delay <= 100) return 'delay-100';
      if (delay <= 200) return 'delay-200';
      if (delay <= 300) return 'delay-300';
      if (delay <= 400) return 'delay-400';
      if (delay <= 500) return 'delay-500';
      if (delay <= 700) return 'delay-700';
      return 'delay-1000';
    }
    return '';
  });
</script>

<div
  bind:this={elementRef}
  class={cn(animationClass, delayClass, className)}
  style="animation-duration: {duration}ms;"
>
  {@render children?.()}
</div>
