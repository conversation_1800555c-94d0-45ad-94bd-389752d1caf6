<script>
  import FeatureCard from '../ui/FeatureCard.svelte';
  import Animated<PERSON>ontainer from '$lib/components/ui/AnimatedContainer.svelte';
  import {
    Zap,
    Target,
    Clock,
    BarChart3,
    Shield,
    Globe,
    MessageSquare,
    Award,
  } from 'lucide-svelte';

  const features = [
    {
      icon: Zap,
      title: 'One-Click Apply',
      description: 'Apply to hundreds of jobs across multiple platforms with a single click.',
    },
    {
      icon: Target,
      title: 'Smart Matching',
      description: 'Our AI matches your resume to job requirements for higher success rates.',
    },
    {
      icon: Clock,
      title: 'Save Hours Daily',
      description: 'Automate repetitive application tasks and focus on preparing for interviews.',
    },
    {
      icon: BarChart3,
      title: 'Application Analytics',
      description: 'Track your application performance and optimize your job search strategy.',
    },
    {
      icon: Shield,
      title: 'Resume Optimization',
      description: 'Get suggestions to improve your resume for specific job postings.',
    },
    {
      icon: Globe,
      title: 'Multi-Platform Support',
      description: 'Works with LinkedIn, Indeed, Glassdoor, ZipRecruiter, and many more.',
    },
    {
      icon: MessageSquare,
      title: 'AI Interview Coach',
      description:
        'Practice with realistic mock interviews tailored to your industry with instant feedback.',
    },
    {
      icon: Award,
      title: 'Career Advancement',
      description: 'Get personalized recommendations for skills to develop for your dream roles.',
    },
  ];
</script>

<section
  id="features"
  class="from-background via-muted/10 to-background relative overflow-hidden bg-gradient-to-br py-20">
  <!-- Background decoration -->
  <div class="absolute inset-0 opacity-5">
    <div class="animate-float absolute right-20 top-20 delay-1000">
      <div class="bg-primary/20 h-32 w-32 rounded-full"></div>
    </div>
    <div class="animate-float absolute bottom-20 left-20 delay-500">
      <div class="bg-accent/20 h-24 w-24 rounded-full"></div>
    </div>
  </div>

  <div class="container relative mx-auto px-4">
    <AnimatedContainer animation="fade-in-up" triggerOnScroll={true}>
      <div class="mb-16 text-center">
        <h2 class="mb-4 text-3xl font-bold md:text-4xl">
          Powerful features to <span class="gradient-text">accelerate</span> your job search
        </h2>
        <p class="text-muted-foreground mx-auto max-w-2xl text-lg">
          Everything you need to find and land your dream job, powered by cutting-edge AI technology
        </p>
      </div>
    </AnimatedContainer>

    <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
      {#each features as feature, index}
        <AnimatedContainer animation="fade-in-up" delay={index * 100} triggerOnScroll={true}>
          <FeatureCard
            icon={feature.icon}
            title={feature.title}
            description={feature.description} />
        </AnimatedContainer>
      {/each}
    </div>
  </div>
</section>
