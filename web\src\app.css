/* Tailwind CSS v4 imports */
@import 'tailwindcss';

@font-face {
  font-family: 'InterVar';
  src: url('/assets/fonts/Inter-VariableFont_opsz,wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'InterVar';
  src: url('/assets/fonts/Inter-Italic-VariableFont_opsz,wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: italic;
  font-display: swap;
}

.font-inter {
  font-family: 'InterVar', sans-serif;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-sans);
  font-size: 1rem;
  line-height: 1.5rem;
  height: 100vh;
  display: flex;
  flex-direction: column;
  transition:
    color 0.3s ease,
    background-color 0.3s ease;
}

/* Modern Animation Utilities */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Delay Classes */
.delay-100 {
  animation-delay: 0.1s;
}
.delay-200 {
  animation-delay: 0.2s;
}
.delay-300 {
  animation-delay: 0.3s;
}
.delay-400 {
  animation-delay: 0.4s;
}
.delay-500 {
  animation-delay: 0.5s;
}
.delay-700 {
  animation-delay: 0.7s;
}
.delay-1000 {
  animation-delay: 1s;
}

/* Modern Gradient Utilities */
.gradient-primary {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--secondary) / 0.8) 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--accent) / 0.8) 100%);
}

.gradient-muted {
  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--muted) / 0.8) 100%);
}

.gradient-card {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.95) 100%);
}

.gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-border {
  position: relative;
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
  padding: 1px;
  border-radius: inherit;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 1px;
  background: hsl(var(--background));
  border-radius: inherit;
}

/* Glass Morphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Modern Shadow Utilities */
.shadow-soft {
  box-shadow:
    0 2px 15px -3px rgba(0, 0, 0, 0.07),
    0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.shadow-medium {
  box-shadow:
    0 4px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-large {
  box-shadow:
    0 10px 40px -10px rgba(0, 0, 0, 0.15),
    0 2px 10px -2px rgba(0, 0, 0, 0.05);
}

.shadow-colored {
  box-shadow:
    0 10px 40px -10px hsl(var(--primary) / 0.3),
    0 2px 10px -2px hsl(var(--primary) / 0.1);
}

/* Hover Effects */
.hover-lift {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px hsl(var(--primary) / 0.5);
}

/* Modern Card Styles */
.modern-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 12px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15);
}

.floating-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 16px;
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Interactive Elements */
.interactive {
  transition: all 0.2s ease;
  cursor: pointer;
}

.interactive:hover {
  transform: translateY(-1px);
}

.interactive:active {
  transform: translateY(0);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-sans);
  font-weight: 700;
}

.section {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.container {
  margin-left: auto;
  margin-right: auto;
  max-width: 80rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.section-title {
  margin-bottom: 1rem;
  text-align: center;
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  color: hsl(var(--foreground));
}

.section-subtitle {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 3rem;
  max-width: 48rem;
  text-align: center;
  font-size: 1.125rem;
  line-height: 1.75rem;
  color: hsl(var(--muted-foreground));
}

.gradient-text {
  background-image: linear-gradient(to right, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@layer base {
  :root {
    --font-sans: 'InterVar', ui-sans-serif, system-ui, sans-serif;
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 210 20% 98%;
    --ring: 224 71.4% 4.1%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Custom theme variables */
    /* Status colors */
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 38 92% 50%;
    --warning-foreground: 355.7 100% 97.3%;
    --info: 221.2 83.2% 53.3%;
    --info-foreground: 355.7 100% 97.3%;
    --error: 0 84.2% 60.2%;
    --error-foreground: 210 40% 98%;

    /* UI element colors */
    --card-hover: 0 0% 98%;
    --command: 0 0% 100%;
    --command-foreground: 224 71.4% 4.1%;
    --command-border: 220 13% 91%;
    --popover-hover: 0 0% 98%;
    --select: 0 0% 100%;
    --select-foreground: 224 71.4% 4.1%;
    --skeleton: 0 0% 95%;
    --skeleton-foreground: 0 0% 90%;
    --tooltip: 224 71.4% 4.1%;
    --tooltip-foreground: 210 20% 98%;

    /* Custom component colors */
    --calendar: 0 0% 100%;
    --calendar-foreground: 224 71.4% 4.1%;
    --calendar-muted: 220 14.3% 95.9%;
    --calendar-muted-foreground: 220 8.9% 46.1%;
    --calendar-today: 220.9 39.3% 11%;

    /* Custom theme colors */
    --grid: 0 0% 100%;
    --grid-foreground: 0 0% 95%;
    --alert: 0 84.2% 60.2%;
    --alert-foreground: 210 40% 98%;
    --highlight: 47.9 95.8% 53.1%;
    --highlight-foreground: 26 83.3% 14.1%;
    --brand: 221.2 83.2% 53.3%;
    --brand-foreground: 210 40% 98%;
    --neutral: 220 8.9% 46.1%;
    --neutral-foreground: 210 40% 98%;

    /* Chart colors */
    --chart-1: 220.9 39.3% 11%;
    --chart-2: 217.2 91.2% 59.8%;
    --chart-3: 142.1 76.2% 36.3%;
    --chart-4: 38 92% 50%;
    --chart-5: 262.1 83.3% 57.8%;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --ring: 216 12.2% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Custom theme variables */
    /* Status colors */
    --success: 142.1 70.2% 29.3%;
    --success-foreground: 144.9 80.4% 10%;
    --warning: 35 92% 33%;
    --warning-foreground: 355.7 100% 97.3%;
    --info: 217.2 91.2% 31.3%;
    --info-foreground: 355.7 100% 97.3%;
    --error: 0 62.8% 30.6%;
    --error-foreground: 210 40% 98%;

    /* UI element colors */
    --card-hover: 224 71.4% 8%;
    --command: 224 71.4% 4.1%;
    --command-foreground: 210 20% 98%;
    --command-border: 215 27.9% 16.9%;
    --popover-hover: 224 71.4% 8%;
    --select: 224 71.4% 4.1%;
    --select-foreground: 210 20% 98%;
    --skeleton: 224 71.4% 8%;
    --skeleton-foreground: 224 71.4% 12%;
    --tooltip: 224 71.4% 4.1%;
    --tooltip-foreground: 210 20% 98%;

    /* Custom component colors */
    --calendar: 224 71.4% 4.1%;
    --calendar-foreground: 210 20% 98%;
    --calendar-muted: 215 27.9% 16.9%;
    --calendar-muted-foreground: 217.9 10.6% 64.9%;
    --calendar-today: 210 20% 98%;

    /* Custom theme colors */
    --grid: 222.2 47.4% 11.2%;
    --grid-foreground: 215 27.9% 16.9%;
    --alert: 0 62.8% 30.6%;
    --alert-foreground: 210 40% 98%;
    --highlight: 47.9 95.8% 42.2%;
    --highlight-foreground: 26 83.3% 14.1%;
    --brand: 217.2 91.2% 31.3%;
    --brand-foreground: 210 40% 98%;
    --neutral: 215 27.9% 16.9%;
    --neutral-foreground: 210 40% 98%;

    /* Chart colors */
    --chart-1: 217.2 91.2% 59.8%;
    --chart-2: 142.1 70.2% 45.3%;
    --chart-3: 38 92% 50%;
    --chart-4: 262.1 83.3% 67.8%;
    --chart-5: 0 72.2% 50.6%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  /* Ensure proper theme application */
  html {
    color-scheme: light;
  }

  html.dark {
    color-scheme: dark;
  }

  /* Apply theme colors to common elements */
  html,
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

@layer components {
  /* Data attribute styles for UI components */
  [data-state='checked'] {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  [data-state='unchecked'] {
    background-color: hsl(var(--input));
  }

  [data-highlighted] {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }

  [data-disabled='true'],
  [data-disabled] {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Additional data attribute styles */
  [data-selected] {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  [data-placeholder] {
    color: hsl(var(--muted-foreground));
  }

  /* Table selected state */
  [data-state='selected'] {
    background-color: hsl(var(--muted));
  }

  /* Aria states */
  [aria-selected='true'] {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }

  /* Sidebar data attributes */
  [data-variant='sidebar'] {
    background-color: hsl(var(--sidebar-background));
    color: hsl(var(--sidebar-foreground));
    border-color: hsl(var(--sidebar-border));
  }

  [data-variant='inset'] {
    background-color: hsl(var(--sidebar-accent));
    color: hsl(var(--sidebar-accent-foreground));
  }

  /* Input OTP data attributes */
  [data-active] {
    box-shadow: 0 0 0 2px hsl(var(--ring));
    outline: none;
  }

  [data-complete] {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  /* Toggle data attributes */
  [data-state='on'] {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }

  [data-state='off'] {
    background-color: transparent;
  }

  /* Focus states */
  [data-focus-visible] {
    box-shadow: 0 0 0 2px hsl(var(--ring));
    outline: none;
  }

  /* Loading and pending states */
  [data-loading],
  [data-pending] {
    opacity: 0.5;
    pointer-events: none;
  }

  /* Error states */
  [data-invalid] {
    border-color: hsl(var(--destructive));
    box-shadow: 0 0 0 1px hsl(var(--destructive));
  }

  [data-error] {
    border-color: hsl(var(--destructive));
    color: hsl(var(--destructive));
  }

  /* Orientation states */
  [data-orientation='horizontal'] {
    flex-direction: row;
  }

  [data-orientation='vertical'] {
    flex-direction: column;
  }

  /* Size variants */
  [data-size='sm'] {
    height: 2rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    font-size: 0.875rem;
  }

  [data-size='md'] {
    height: 2.5rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  [data-size='lg'] {
    height: 3rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-size: 1.125rem;
  }

  /* Sonner toast styling for proper dark mode support */
  .toaster {
    --normal-bg: hsl(var(--popover));
    --normal-border: hsl(var(--border));
    --normal-text: hsl(var(--popover-foreground));
    --success-bg: hsl(var(--success));
    --success-border: hsl(var(--success));
    --success-text: hsl(var(--success-foreground));
    --error-bg: hsl(var(--destructive));
    --error-border: hsl(var(--destructive));
    --error-text: hsl(var(--destructive-foreground));
    --warning-bg: hsl(var(--warning));
    --warning-border: hsl(var(--warning));
    --warning-text: hsl(var(--warning-foreground));
    --info-bg: hsl(var(--info));
    --info-border: hsl(var(--info));
    --info-text: hsl(var(--info-foreground));
  }

  .toaster [data-sonner-toast] {
    background: var(--normal-bg) !important;
    border: 1px solid var(--normal-border) !important;
    color: var(--normal-text) !important;
  }

  .toaster [data-sonner-toast][data-type='success'] {
    background: var(--success-bg) !important;
    border-color: var(--success-border) !important;
    color: var(--success-text) !important;
  }

  .toaster [data-sonner-toast][data-type='error'] {
    background: var(--error-bg) !important;
    border-color: var(--error-border) !important;
    color: var(--error-text) !important;
  }

  .toaster [data-sonner-toast][data-type='warning'] {
    background: var(--warning-bg) !important;
    border-color: var(--warning-border) !important;
    color: var(--warning-text) !important;
  }

  .toaster [data-sonner-toast][data-type='info'] {
    background: var(--info-bg) !important;
    border-color: var(--info-border) !important;
    color: var(--info-text) !important;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
